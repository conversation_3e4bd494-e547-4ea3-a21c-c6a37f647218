# 🖱️ AI Virtual Mouse using OpenCV & MediaPipe

Control your computer mouse using just your hand gestures via webcam!  
This project uses **OpenCV**, **MediaPipe**, and **PyAutoGUI** to detect hand landmarks and translate them into mouse actions.

---

## 📸 Demo

> *(Optional)*  
> Add a screenshot or screen recording GIF of the virtual mouse in action.

---

## 🚀 Features

- ✅ Real-time webcam hand tracking
- 🖱️ Cursor movement using index finger
- 👆 Auto click using pinch gesture (thumb + index)
- ❄️ Cooldown to avoid accidental repeated clicks
- 🔁 Smooth and intuitive interaction

---

## 💻 Tech Stack

- **Python 3.x**
- [OpenCV](https://pypi.org/project/opencv-python/)
- [MediaPipe](https://google.github.io/mediapipe/)
- [PyAutoGUI](https://pyautogui.readthedocs.io/)

---

## ⚙️ Installation

Clone the repository:

```bash
git clone https://github.com/Shubhamcs074/ai-virtual-mouse.git
cd ai-virtual-mouse
